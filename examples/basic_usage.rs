//! Basic usage example for the ComfyUI Inference Engine.

use comfyui_inference_engine::tensor::{zeros, ones, from_vec, <PERSON>sor<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>sor};
use comfyui_inference_engine::transformer::TransformerConfig;
use comfyui_inference_engine::error::TensorError;

fn main() -> Result<(), TensorError> {
    println!("ComfyUI Inference Engine v{}", comfyui_inference_engine::VERSION);

    // Create some tensors
    let shape = Shape::new(vec![2, 3]);
    println!("Created shape: {}", shape);

    // Create a tensor filled with zeros
    let zeros_tensor = zeros::<f32>(&shape)?;
    println!("Zeros tensor shape: {:?}", zeros_tensor.shape().dims());
    println!("Zeros tensor size: {}", zeros_tensor.size());

    // Create a tensor filled with ones
    let ones_tensor = ones::<f32>(&shape)?;
    println!("Ones tensor shape: {:?}", ones_tensor.shape().dims());

    // Create a tensor from data
    let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
    let data_tensor = from_vec(data, &shape)?;
    println!("Data tensor: {:?}", data_tensor.to_vec());

    // Perform tensor operations
    let sum_tensor = zeros_tensor.add(&ones_tensor)?;
    println!("Sum result: {:?}", sum_tensor.to_vec());

    let scaled_tensor = data_tensor.mul_scalar(2.0)?;
    println!("Scaled tensor: {:?}", scaled_tensor.to_vec());

    // Test activation functions
    let relu_result = data_tensor.relu()?;
    println!("ReLU result: {:?}", relu_result.to_vec());

    // Test matrix multiplication
    let matrix_a = from_vec(vec![1.0, 2.0, 3.0, 4.0], &Shape::new(vec![2, 2]))?;
    let matrix_b = from_vec(vec![5.0, 6.0, 7.0, 8.0], &Shape::new(vec![2, 2]))?;
    let matmul_result = matrix_a.matmul(&matrix_b)?;
    println!("Matrix multiplication result: {:?}", matmul_result.to_vec());

    // Test tensor reshaping
    let reshaped = data_tensor.reshape(&Shape::new(vec![3, 2]))?;
    println!("Reshaped tensor shape: {:?}", reshaped.shape().dims());

    // Create a transformer configuration
    let config = TransformerConfig::default();
    println!("Transformer config:");
    println!("  Vocab size: {}", config.vocab_size);
    println!("  Hidden size: {}", config.hidden_size);
    println!("  Num layers: {}", config.num_layers);
    println!("  Num attention heads: {}", config.num_attention_heads);

    println!("Basic usage example completed successfully!");

    Ok(())
}
