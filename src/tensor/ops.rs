//! Tensor operations implementation.

use crate::tensor::{Tensor, TensorOps, Numeric};
use crate::tensor::cpu::CpuTensor;
use crate::error::{TensorError, ErrorContext};

impl<T: Numeric> TensorOps<T> for CpuTensor<T> {
    fn add(&self, other: &Self) -> Result<Self, Self::Error> {
        // Check shape compatibility
        let result_shape = self.shape().broadcast_with(other.shape())?;
        
        // For now, implement element-wise addition for same-shaped tensors
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("add", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a + b)
            .collect();
        
        CpuTensor::from_data(result_data, result_shape)
    }
    
    fn sub(&self, other: &Self) -> Result<Self, Self::Error> {
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("sub", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a - b)
            .collect();
        
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn mul(&self, other: &Self) -> Result<Self, Self::Error> {
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("mul", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a * b)
            .collect();
        
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn div(&self, other: &Self) -> Result<Self, Self::Error> {
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("div", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result_data: Vec<T> = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a / b)
            .collect();
        
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn add_scalar(&self, scalar: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x + scalar).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn sub_scalar(&self, scalar: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x - scalar).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn mul_scalar(&self, scalar: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x * scalar).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn div_scalar(&self, scalar: T) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x / scalar).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn matmul(&self, other: &Self) -> Result<Self, Self::Error> {
        // Simplified matrix multiplication for 2D tensors
        if self.shape().rank() != 2 || other.shape().rank() != 2 {
            return Err(TensorError::InvalidDimension {
                dimension: self.shape().rank().min(other.shape().rank()),
                total_dims: 2,
                context: Some(ErrorContext::new("matmul", "tensor::ops")),
            });
        }
        
        let [m, k] = [self.shape().dims()[0], self.shape().dims()[1]];
        let [k2, n] = [other.shape().dims()[0], other.shape().dims()[1]];
        
        if k != k2 {
            return Err(TensorError::ShapeMismatch {
                expected: vec![k],
                actual: vec![k2],
                context: Some(ErrorContext::new("matmul", "tensor::ops")),
            });
        }
        
        let mut result = vec![T::ZERO; m * n];
        let a_data = self.data();
        let b_data = other.data();
        
        for i in 0..m {
            for j in 0..n {
                let mut sum = T::ZERO;
                for k_idx in 0..k {
                    let a_val = a_data[i * k + k_idx];
                    let b_val = b_data[k_idx * n + j];
                    sum = sum + a_val * b_val;
                }
                result[i * n + j] = sum;
            }
        }
        
        let result_shape = crate::tensor::Shape::new(vec![m, n]);
        CpuTensor::from_data(result, result_shape)
    }
    
    fn dot(&self, other: &Self) -> Result<T, Self::Error> {
        if self.shape() != other.shape() {
            return Err(TensorError::ShapeMismatch {
                expected: self.shape().dims().to_vec(),
                actual: other.shape().dims().to_vec(),
                context: Some(ErrorContext::new("dot", "tensor::ops")),
            });
        }
        
        let self_data = self.data();
        let other_data = other.data();
        let result = self_data.iter()
            .zip(other_data.iter())
            .map(|(&a, &b)| a * b)
            .fold(T::ZERO, |acc, x| acc + x);
        
        Ok(result)
    }
    
    fn sum(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error> {
        match dim {
            None => {
                // Sum all elements
                let data = self.data();
                let total = data.iter().fold(T::ZERO, |acc, &x| acc + x);
                let result_shape = if keepdim {
                    crate::tensor::Shape::new(vec![1; self.shape().rank()])
                } else {
                    crate::tensor::Shape::new(vec![])
                };
                CpuTensor::from_data(vec![total], result_shape)
            }
            Some(_dim) => {
                // TODO: Implement dimension-specific sum
                Err(TensorError::DataTypeIncompatible {
                    operation: "sum with dimension".to_string(),
                    dtype: "not implemented".to_string(),
                    context: Some(ErrorContext::new("sum", "tensor::ops")),
                })
            }
        }
    }
    
    fn mean(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error> {
        let sum_result = self.sum(dim, keepdim)?;
        let count = T::from_f32(self.size() as f32);
        sum_result.div_scalar(count)
    }
    
    fn max(&self, _dim: Option<usize>, _keepdim: bool) -> Result<Self, Self::Error> {
        // TODO: Implement max operation
        Err(TensorError::DataTypeIncompatible {
            operation: "max".to_string(),
            dtype: "not implemented".to_string(),
            context: Some(ErrorContext::new("max", "tensor::ops")),
        })
    }
    
    fn min(&self, _dim: Option<usize>, _keepdim: bool) -> Result<Self, Self::Error> {
        // TODO: Implement min operation
        Err(TensorError::DataTypeIncompatible {
            operation: "min".to_string(),
            dtype: "not implemented".to_string(),
            context: Some(ErrorContext::new("min", "tensor::ops")),
        })
    }
    
    fn relu(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter()
            .map(|&x| x.max(T::ZERO))
            .collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn gelu(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter()
            .map(|&x| {
                // GELU(x) = 0.5 * x * (1 + tanh(sqrt(2/π) * (x + 0.044715 * x^3)))
                let x_f32 = x.to_f32();
                let sqrt_2_over_pi = (2.0 / std::f32::consts::PI).sqrt();
                let inner = sqrt_2_over_pi * (x_f32 + 0.044715 * x_f32.powi(3));
                let gelu_val = 0.5 * x_f32 * (1.0 + inner.tanh());
                T::from_f32(gelu_val)
            })
            .collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn softmax(&self, _dim: usize) -> Result<Self, Self::Error> {
        // TODO: Implement softmax
        Err(TensorError::DataTypeIncompatible {
            operation: "softmax".to_string(),
            dtype: "not implemented".to_string(),
            context: Some(ErrorContext::new("softmax", "tensor::ops")),
        })
    }
    
    fn log_softmax(&self, _dim: usize) -> Result<Self, Self::Error> {
        // TODO: Implement log_softmax
        Err(TensorError::DataTypeIncompatible {
            operation: "log_softmax".to_string(),
            dtype: "not implemented".to_string(),
            context: Some(ErrorContext::new("log_softmax", "tensor::ops")),
        })
    }
    
    fn tanh(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter().map(|&x| x.tanh()).collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
    
    fn sigmoid(&self) -> Result<Self, Self::Error> {
        let data = self.data();
        let result_data: Vec<T> = data.iter()
            .map(|&x| {
                let x_f32 = x.to_f32();
                let sigmoid_val = 1.0 / (1.0 + (-x_f32).exp());
                T::from_f32(sigmoid_val)
            })
            .collect();
        CpuTensor::from_data(result_data, self.shape().clone())
    }
}
