//! Tensor operations and data structures.
//!
//! This module provides:
//! - Multi-dimensional tensor abstraction
//! - Efficient memory layout and access patterns
//! - SIMD-optimized mathematical operations
//! - Device abstraction for CPU/GPU computing
//! - Type-safe shape validation

use std::fmt;
use std::ops::{Add, Mul, Sub, Div};
use crate::error::TensorError;

pub mod shape;
pub mod storage;
pub mod ops;
pub mod cpu;
pub mod device;

#[cfg(feature = "cuda")]
pub mod cuda;

#[cfg(feature = "metal")]
pub mod metal;

pub use shape::*;
pub use storage::*;
pub use device::*;

/// Supported data types for tensors.
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash)]
pub enum DType {
    /// 32-bit floating point.
    F32,
    /// 64-bit floating point.
    F64,
    /// 16-bit floating point (half precision).
    #[cfg(feature = "f16")]
    F16,
    /// 32-bit signed integer.
    I32,
    /// 64-bit signed integer.
    I64,
}

impl fmt::Display for DType {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DType::F32 => write!(f, "f32"),
            DType::F64 => write!(f, "f64"),
            #[cfg(feature = "f16")]
            DType::F16 => write!(f, "f16"),
            DType::I32 => write!(f, "i32"),
            DType::I64 => write!(f, "i64"),
        }
    }
}

/// Trait for numeric types that can be used in tensors.
pub trait Numeric: 
    Copy + Clone + Send + Sync + 'static + 
    Add<Output = Self> + Sub<Output = Self> + 
    Mul<Output = Self> + Div<Output = Self> +
    PartialEq + PartialOrd + fmt::Debug + fmt::Display
{
    /// Zero value for this type.
    const ZERO: Self;
    /// One value for this type.
    const ONE: Self;
    /// Negative infinity (for floating point types).
    const NEG_INFINITY: Self;
    /// Positive infinity (for floating point types).
    const POS_INFINITY: Self;
    
    /// Get the data type enum for this numeric type.
    fn dtype() -> DType;
    
    /// Convert from f32.
    fn from_f32(value: f32) -> Self;
    
    /// Convert to f32.
    fn to_f32(self) -> f32;
    
    /// Square root function.
    fn sqrt(self) -> Self;
    
    /// Exponential function.
    fn exp(self) -> Self;
    
    /// Natural logarithm.
    fn ln(self) -> Self;
    
    /// Hyperbolic tangent.
    fn tanh(self) -> Self;
    
    /// Maximum of two values.
    fn max(self, other: Self) -> Self;
    
    /// Minimum of two values.
    fn min(self, other: Self) -> Self;
    
    /// Absolute value.
    fn abs(self) -> Self;
}

impl Numeric for f32 {
    const ZERO: Self = 0.0;
    const ONE: Self = 1.0;
    const NEG_INFINITY: Self = f32::NEG_INFINITY;
    const POS_INFINITY: Self = f32::INFINITY;
    
    fn dtype() -> DType { DType::F32 }
    fn from_f32(value: f32) -> Self { value }
    fn to_f32(self) -> f32 { self }
    fn sqrt(self) -> Self { self.sqrt() }
    fn exp(self) -> Self { self.exp() }
    fn ln(self) -> Self { self.ln() }
    fn tanh(self) -> Self { self.tanh() }
    fn max(self, other: Self) -> Self { self.max(other) }
    fn min(self, other: Self) -> Self { self.min(other) }
    fn abs(self) -> Self { self.abs() }
}

impl Numeric for f64 {
    const ZERO: Self = 0.0;
    const ONE: Self = 1.0;
    const NEG_INFINITY: Self = f64::NEG_INFINITY;
    const POS_INFINITY: Self = f64::INFINITY;
    
    fn dtype() -> DType { DType::F64 }
    fn from_f32(value: f32) -> Self { value as f64 }
    fn to_f32(self) -> f32 { self as f32 }
    fn sqrt(self) -> Self { self.sqrt() }
    fn exp(self) -> Self { self.exp() }
    fn ln(self) -> Self { self.ln() }
    fn tanh(self) -> Self { self.tanh() }
    fn max(self, other: Self) -> Self { self.max(other) }
    fn min(self, other: Self) -> Self { self.min(other) }
    fn abs(self) -> Self { self.abs() }
}

/// Core tensor trait defining the interface for all tensor implementations.
pub trait Tensor<T: Numeric>: Clone + Send + Sync {
    /// Error type for tensor operations.
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Get the shape of this tensor.
    fn shape(&self) -> &Shape;
    
    /// Get the data type of this tensor.
    fn dtype(&self) -> DType;
    
    /// Check if the tensor data is stored contiguously in memory.
    fn is_contiguous(&self) -> bool;
    
    /// Get the total number of elements in this tensor.
    fn size(&self) -> usize {
        self.shape().size()
    }
    
    /// Get the number of dimensions (rank) of this tensor.
    fn rank(&self) -> usize {
        self.shape().rank()
    }
    
    /// Reshape the tensor to a new shape.
    fn reshape(&self, shape: &Shape) -> Result<Self, Self::Error>;
    
    /// Transpose the tensor by swapping two dimensions.
    fn transpose(&self, dim1: usize, dim2: usize) -> Result<Self, Self::Error>;
    
    /// Permute the dimensions of the tensor.
    fn permute(&self, dims: &[usize]) -> Result<Self, Self::Error>;
    
    /// Remove dimensions of size 1.
    fn squeeze(&self, dim: Option<usize>) -> Result<Self, Self::Error>;
    
    /// Add a dimension of size 1.
    fn unsqueeze(&self, dim: usize) -> Result<Self, Self::Error>;
    
    /// Extract a slice of the tensor.
    fn slice(&self, ranges: &[std::ops::Range<usize>]) -> Result<Self, Self::Error>;
    
    /// Select elements along a dimension using indices.
    fn index_select(&self, dim: usize, indices: &[usize]) -> Result<Self, Self::Error>;
}

/// Trait for tensor mathematical operations.
pub trait TensorOps<T: Numeric>: Tensor<T> {
    // Element-wise operations
    fn add(&self, other: &Self) -> Result<Self, Self::Error>;
    fn sub(&self, other: &Self) -> Result<Self, Self::Error>;
    fn mul(&self, other: &Self) -> Result<Self, Self::Error>;
    fn div(&self, other: &Self) -> Result<Self, Self::Error>;
    
    // Scalar operations
    fn add_scalar(&self, scalar: T) -> Result<Self, Self::Error>;
    fn sub_scalar(&self, scalar: T) -> Result<Self, Self::Error>;
    fn mul_scalar(&self, scalar: T) -> Result<Self, Self::Error>;
    fn div_scalar(&self, scalar: T) -> Result<Self, Self::Error>;
    
    // Matrix operations
    fn matmul(&self, other: &Self) -> Result<Self, Self::Error>;
    fn dot(&self, other: &Self) -> Result<T, Self::Error>;
    
    // Reduction operations
    fn sum(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;
    fn mean(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;
    fn max(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;
    fn min(&self, dim: Option<usize>, keepdim: bool) -> Result<Self, Self::Error>;
    
    // Activation functions
    fn relu(&self) -> Result<Self, Self::Error>;
    fn gelu(&self) -> Result<Self, Self::Error>;
    fn softmax(&self, dim: usize) -> Result<Self, Self::Error>;
    fn log_softmax(&self, dim: usize) -> Result<Self, Self::Error>;
    fn tanh(&self) -> Result<Self, Self::Error>;
    fn sigmoid(&self) -> Result<Self, Self::Error>;
}

/// Factory functions for creating tensors.
pub trait TensorFactory<T: Numeric> {
    type TensorType: Tensor<T>;
    type Error: std::error::Error + Send + Sync + 'static;
    
    /// Create a tensor filled with zeros.
    fn zeros(shape: &Shape) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a tensor filled with ones.
    fn ones(shape: &Shape) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a tensor filled with a constant value.
    fn full(shape: &Shape, value: T) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a tensor from a slice of data.
    fn from_slice(data: &[T], shape: &Shape) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a tensor from a vector of data.
    fn from_vec(data: Vec<T>, shape: &Shape) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a random tensor with values from a normal distribution.
    fn randn(shape: &Shape, mean: T, std: T) -> Result<Self::TensorType, Self::Error>;
    
    /// Create a random tensor with values from a uniform distribution.
    fn rand(shape: &Shape, low: T, high: T) -> Result<Self::TensorType, Self::Error>;
}

/// Convenience functions for creating tensors.
pub fn zeros<T: Numeric>(shape: &Shape) -> Result<cpu::CpuTensor<T>, TensorError> {
    cpu::CpuTensorFactory::zeros(shape)
}

pub fn ones<T: Numeric>(shape: &Shape) -> Result<cpu::CpuTensor<T>, TensorError> {
    cpu::CpuTensorFactory::ones(shape)
}

pub fn from_slice<T: Numeric>(data: &[T], shape: &Shape) -> Result<cpu::CpuTensor<T>, TensorError> {
    cpu::CpuTensorFactory::from_slice(data, shape)
}

pub fn from_vec<T: Numeric>(data: Vec<T>, shape: &Shape) -> Result<cpu::CpuTensor<T>, TensorError> {
    cpu::CpuTensorFactory::from_vec(data, shape)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_dtype_display() {
        assert_eq!(format!("{}", DType::F32), "f32");
        assert_eq!(format!("{}", DType::F64), "f64");
        assert_eq!(format!("{}", DType::I32), "i32");
    }
    
    #[test]
    fn test_numeric_traits() {
        assert_eq!(f32::ZERO, 0.0);
        assert_eq!(f32::ONE, 1.0);
        assert_eq!(f32::dtype(), DType::F32);
        
        assert_eq!(f64::ZERO, 0.0);
        assert_eq!(f64::ONE, 1.0);
        assert_eq!(f64::dtype(), DType::F64);
    }
}
