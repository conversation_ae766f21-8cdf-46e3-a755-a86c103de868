//! CPU tensor implementation.

use std::marker::PhantomData;
use std::sync::Arc;
use crate::error::{TensorError, ErrorContext};
use crate::tensor::{Tensor, TensorFactory, Numeric, DType, Shape};

/// CPU-based tensor storage.
#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct CpuStorage<T> {
    data: Arc<Vec<T>>,
}

impl<T: Numeric> CpuStorage<T> {
    pub fn new(data: Vec<T>) -> Self {
        Self {
            data: Arc::new(data),
        }
    }
    
    pub fn as_slice(&self) -> &[T] {
        &self.data
    }
    
    pub fn len(&self) -> usize {
        self.data.len()
    }
}

/// CPU tensor implementation.
#[derive(Debug, Clone)]
pub struct CpuTensor<T: Numeric> {
    storage: CpuStorage<T>,
    shape: Shape,
    offset: usize,
    _phantom: PhantomData<T>,
}

impl<T: Numeric> CpuTensor<T> {
    /// Create a new CPU tensor with the given storage and shape.
    pub fn new(storage: CpuStorage<T>, shape: Shape) -> Result<Self, TensorError> {
        if storage.len() < shape.size() {
            return Err(TensorError::MemoryAllocation {
                requested_bytes: shape.size() * std::mem::size_of::<T>(),
                available_bytes: Some(storage.len() * std::mem::size_of::<T>()),
                context: Some(ErrorContext::new("cpu_tensor_new", "tensor::cpu")),
            });
        }
        
        Ok(Self {
            storage,
            shape,
            offset: 0,
            _phantom: PhantomData,
        })
    }
    
    /// Create a tensor from raw data and shape.
    pub fn from_data(data: Vec<T>, shape: Shape) -> Result<Self, TensorError> {
        let storage = CpuStorage::new(data);
        Self::new(storage, shape)
    }
    
    /// Get a reference to the underlying data.
    pub fn data(&self) -> &[T] {
        let start = self.offset;
        let end = start + self.shape.size();
        &self.storage.as_slice()[start..end]
    }
    
    /// Get the value at the specified indices.
    pub fn get(&self, indices: &[usize]) -> Result<T, TensorError> {
        if indices.len() != self.shape.rank() {
            return Err(TensorError::InvalidDimension {
                dimension: indices.len(),
                total_dims: self.shape.rank(),
                context: Some(ErrorContext::new("tensor_get", "tensor::cpu")),
            });
        }
        
        for (i, &index) in indices.iter().enumerate() {
            if index >= self.shape.dims()[i] {
                return Err(TensorError::IndexOutOfBounds {
                    index,
                    size: self.shape.dims()[i],
                    context: Some(ErrorContext::new("tensor_get", "tensor::cpu")
                        .with_info("dimension", i.to_string())),
                });
            }
        }
        
        let linear_index = self.shape.compute_linear_index(indices);
        Ok(self.storage.as_slice()[self.offset + linear_index])
    }
    
    /// Convert to a vector (copies data).
    pub fn to_vec(&self) -> Vec<T> {
        self.data().to_vec()
    }
}

impl<T: Numeric> Tensor<T> for CpuTensor<T> {
    type Error = TensorError;
    
    fn shape(&self) -> &Shape {
        &self.shape
    }
    
    fn dtype(&self) -> DType {
        T::dtype()
    }
    
    fn is_contiguous(&self) -> bool {
        self.shape.is_contiguous() && self.offset == 0
    }
    
    fn reshape(&self, shape: &Shape) -> Result<Self, Self::Error> {
        if shape.size() != self.shape.size() {
            return Err(TensorError::ShapeMismatch {
                expected: vec![self.shape.size()],
                actual: vec![shape.size()],
                context: Some(ErrorContext::new("reshape", "tensor::cpu")),
            });
        }
        
        Ok(Self {
            storage: self.storage.clone(),
            shape: shape.clone(),
            offset: self.offset,
            _phantom: PhantomData,
        })
    }
    
    fn transpose(&self, dim1: usize, dim2: usize) -> Result<Self, Self::Error> {
        let new_shape = self.shape.transpose(dim1, dim2)?;
        
        Ok(Self {
            storage: self.storage.clone(),
            shape: new_shape,
            offset: self.offset,
            _phantom: PhantomData,
        })
    }
    
    fn permute(&self, dims: &[usize]) -> Result<Self, Self::Error> {
        let new_shape = self.shape.permute(dims)?;
        
        Ok(Self {
            storage: self.storage.clone(),
            shape: new_shape,
            offset: self.offset,
            _phantom: PhantomData,
        })
    }
    
    fn squeeze(&self, dim: Option<usize>) -> Result<Self, Self::Error> {
        let new_shape = self.shape.squeeze(dim)?;
        
        Ok(Self {
            storage: self.storage.clone(),
            shape: new_shape,
            offset: self.offset,
            _phantom: PhantomData,
        })
    }
    
    fn unsqueeze(&self, dim: usize) -> Result<Self, Self::Error> {
        let new_shape = self.shape.unsqueeze(dim)?;
        
        Ok(Self {
            storage: self.storage.clone(),
            shape: new_shape,
            offset: self.offset,
            _phantom: PhantomData,
        })
    }
    
    fn slice(&self, ranges: &[std::ops::Range<usize>]) -> Result<Self, Self::Error> {
        if ranges.len() != self.shape.rank() {
            return Err(TensorError::InvalidDimension {
                dimension: ranges.len(),
                total_dims: self.shape.rank(),
                context: Some(ErrorContext::new("slice", "tensor::cpu")),
            });
        }
        
        // Validate ranges and compute new shape
        let mut new_dims = Vec::new();
        let mut new_offset = self.offset;
        
        for (i, range) in ranges.iter().enumerate() {
            if range.end > self.shape.dims()[i] {
                return Err(TensorError::IndexOutOfBounds {
                    index: range.end,
                    size: self.shape.dims()[i],
                    context: Some(ErrorContext::new("slice", "tensor::cpu")
                        .with_info("dimension", i.to_string())),
                });
            }
            
            if range.start >= range.end {
                return Err(TensorError::InvalidDimension {
                    dimension: range.start,
                    total_dims: range.end,
                    context: Some(ErrorContext::new("slice", "tensor::cpu")
                        .with_info("invalid_range", format!("{:?}", range))),
                });
            }
            
            new_dims.push(range.end - range.start);
            new_offset += range.start * self.shape.strides()[i];
        }
        
        let new_shape = Shape::new(new_dims);
        
        Ok(Self {
            storage: self.storage.clone(),
            shape: new_shape,
            offset: new_offset,
            _phantom: PhantomData,
        })
    }
    
    fn index_select(&self, dim: usize, indices: &[usize]) -> Result<Self, Self::Error> {
        if dim >= self.shape.rank() {
            return Err(TensorError::InvalidDimension {
                dimension: dim,
                total_dims: self.shape.rank(),
                context: Some(ErrorContext::new("index_select", "tensor::cpu")),
            });
        }
        
        // Validate indices
        for &index in indices {
            if index >= self.shape.dims()[dim] {
                return Err(TensorError::IndexOutOfBounds {
                    index,
                    size: self.shape.dims()[dim],
                    context: Some(ErrorContext::new("index_select", "tensor::cpu")),
                });
            }
        }
        
        // Create new shape
        let mut new_dims = self.shape.dims().to_vec();
        new_dims[dim] = indices.len();
        let new_shape = Shape::new(new_dims);
        
        // For now, create a new tensor with copied data
        // TODO: Implement more efficient view-based index selection
        let mut new_data = Vec::with_capacity(new_shape.size());
        
        // This is a simplified implementation - a full implementation would
        // need to handle arbitrary dimensional indexing more efficiently
        for &index in indices {
            // Copy the slice at the specified index
            // This is a placeholder - real implementation would be more complex
            let slice_size = self.shape.size() / self.shape.dims()[dim];
            let start_idx = index * slice_size;
            let end_idx = start_idx + slice_size;
            new_data.extend_from_slice(&self.storage.as_slice()[start_idx..end_idx]);
        }
        
        let new_storage = CpuStorage::new(new_data);
        Ok(Self {
            storage: new_storage,
            shape: new_shape,
            offset: 0,
            _phantom: PhantomData,
        })
    }
}

/// Factory for creating CPU tensors.
pub struct CpuTensorFactory;

impl<T: Numeric> TensorFactory<T> for CpuTensorFactory {
    type TensorType = CpuTensor<T>;
    type Error = TensorError;
    
    fn zeros(shape: &Shape) -> Result<Self::TensorType, Self::Error> {
        let data = vec![T::ZERO; shape.size()];
        CpuTensor::from_data(data, shape.clone())
    }
    
    fn ones(shape: &Shape) -> Result<Self::TensorType, Self::Error> {
        let data = vec![T::ONE; shape.size()];
        CpuTensor::from_data(data, shape.clone())
    }
    
    fn full(shape: &Shape, value: T) -> Result<Self::TensorType, Self::Error> {
        let data = vec![value; shape.size()];
        CpuTensor::from_data(data, shape.clone())
    }
    
    fn from_slice(data: &[T], shape: &Shape) -> Result<Self::TensorType, Self::Error> {
        if data.len() != shape.size() {
            return Err(TensorError::ShapeMismatch {
                expected: vec![shape.size()],
                actual: vec![data.len()],
                context: Some(ErrorContext::new("from_slice", "tensor::cpu")),
            });
        }
        
        CpuTensor::from_data(data.to_vec(), shape.clone())
    }
    
    fn from_vec(data: Vec<T>, shape: &Shape) -> Result<Self::TensorType, Self::Error> {
        if data.len() != shape.size() {
            return Err(TensorError::ShapeMismatch {
                expected: vec![shape.size()],
                actual: vec![data.len()],
                context: Some(ErrorContext::new("from_vec", "tensor::cpu")),
            });
        }
        
        CpuTensor::from_data(data, shape.clone())
    }
    
    fn randn(shape: &Shape, mean: T, _std: T) -> Result<Self::TensorType, Self::Error> {
        // Placeholder implementation - would use proper random number generation
        let data = vec![mean; shape.size()];
        CpuTensor::from_data(data, shape.clone())
    }

    fn rand(shape: &Shape, low: T, _high: T) -> Result<Self::TensorType, Self::Error> {
        // Placeholder implementation - would use proper random number generation
        let data = vec![low; shape.size()];
        CpuTensor::from_data(data, shape.clone())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cpu_tensor_creation() {
        let shape = Shape::new(vec![2, 3]);
        let tensor: CpuTensor<f32> = CpuTensorFactory::zeros(&shape).unwrap();

        assert_eq!(tensor.shape().dims(), &[2, 3]);
        assert_eq!(tensor.size(), 6);
        assert_eq!(tensor.dtype(), DType::F32);
    }
    
    #[test]
    fn test_tensor_get() {
        let data = vec![1.0, 2.0, 3.0, 4.0, 5.0, 6.0];
        let shape = Shape::new(vec![2, 3]);
        let tensor = CpuTensor::from_data(data, shape).unwrap();
        
        assert_eq!(tensor.get(&[0, 0]).unwrap(), 1.0);
        assert_eq!(tensor.get(&[0, 1]).unwrap(), 2.0);
        assert_eq!(tensor.get(&[1, 0]).unwrap(), 4.0);
        assert_eq!(tensor.get(&[1, 2]).unwrap(), 6.0);
    }
    
    #[test]
    fn test_tensor_reshape() {
        let shape = Shape::new(vec![2, 3]);
        let tensor: CpuTensor<f32> = CpuTensorFactory::ones(&shape).unwrap();

        let reshaped = tensor.reshape(&Shape::new(vec![6])).unwrap();
        assert_eq!(reshaped.shape().dims(), &[6]);
        assert_eq!(reshaped.size(), 6);
    }
}
