//! Tensor operations benchmarks.

#[cfg(feature = "benchmarks")]
use criterion::{black_box, criterion_group, criterion_main, Criterion};

#[cfg(feature = "benchmarks")]
use comfyui_inference_engine::tensor::*;

#[cfg(feature = "benchmarks")]
fn bench_tensor_creation(c: &mut Criterion) {
    c.bench_function("tensor_zeros_1024", |b| {
        b.iter(|| {
            let shape = Shape::new(vec![32, 32]);
            black_box(zeros::<f32>(&shape).unwrap())
        })
    });
}

#[cfg(feature = "benchmarks")]
fn bench_tensor_matmul(c: &mut Criterion) {
    let shape_a = Shape::new(vec![64, 128]);
    let shape_b = Shape::new(vec![128, 64]);
    let a = zeros::<f32>(&shape_a).unwrap();
    let b = zeros::<f32>(&shape_b).unwrap();
    
    c.bench_function("matmul_64x128_128x64", |b| {
        b.iter(|| {
            black_box(a.matmul(&b).unwrap())
        })
    });
}

#[cfg(feature = "benchmarks")]
criterion_group!(benches, bench_tensor_creation, bench_tensor_matmul);

#[cfg(feature = "benchmarks")]
criterion_main!(benches);

#[cfg(not(feature = "benchmarks"))]
fn main() {
    println!("Benchmarks require the 'benchmarks' feature to be enabled");
}
