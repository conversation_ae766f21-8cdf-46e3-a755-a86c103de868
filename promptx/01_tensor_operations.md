# 提示词 01: 完善张量操作系统

## 任务描述
在现有的ComfyUI推理引擎基础上，完善张量操作系统，实现更多数学函数、归约操作和高级张量操作。

## 背景信息
当前项目已经实现了基础的张量创建、形状操作和简单数学运算。需要扩展更多的张量操作来支持完整的深度学习计算需求。

## 具体要求

### 1. 数学函数扩展
请在 `src/tensor/ops.rs` 中实现以下数学函数：
- **三角函数**: sin, cos, tan, asin, acos, atan
- **指数对数**: exp, log, log2, log10, sqrt, pow
- **双曲函数**: sinh, cosh, tanh
- **舍入函数**: floor, ceil, round, trunc
- **比较函数**: max, min, clamp

### 2. 归约操作
在 `src/tensor/reduction.rs` 中实现：
- **统计归约**: sum, mean, std, var
- **极值归约**: max, min, argmax, argmin
- **逻辑归约**: all, any
- **范数计算**: norm (L1, L2, Frobenius)
- **沿轴归约**: 支持指定维度的归约操作

### 3. 高级张量操作
在 `src/tensor/advanced.rs` 中实现：
- **广播机制**: 自动形状广播和兼容性检查
- **张量拼接**: cat, stack, split, chunk
- **索引操作**: gather, scatter, index_select
- **掩码操作**: masked_fill, masked_select
- **排序操作**: sort, argsort, topk

### 4. 内存优化
- **就地操作**: 为所有操作提供 `_inplace` 版本
- **视图操作**: 实现零拷贝的视图创建
- **内存池**: 优化临时张量的内存分配

## 技术要求

### 性能优化
- 使用SIMD指令优化数学运算
- 实现多线程并行计算
- 优化内存访问模式
- 添加缓存友好的数据布局

### 错误处理
- 完善形状兼容性检查
- 添加数值稳定性验证
- 实现溢出和下溢检测
- 提供详细的错误上下文

### 测试覆盖
- 为每个操作编写单元测试
- 添加性能基准测试
- 实现数值精度验证
- 测试边界条件和错误情况

## 代码结构建议

```rust
// src/tensor/ops.rs - 数学函数
pub trait MathOps<T: Numeric> {
    fn sin(&self) -> Result<Self, TensorError>;
    fn cos(&self) -> Result<Self, TensorError>;
    fn exp(&self) -> Result<Self, TensorError>;
    // ... 其他数学函数
}

// src/tensor/reduction.rs - 归约操作
pub trait ReductionOps<T: Numeric> {
    fn sum(&self, dim: Option<usize>) -> Result<Self, TensorError>;
    fn mean(&self, dim: Option<usize>) -> Result<Self, TensorError>;
    fn max(&self, dim: Option<usize>) -> Result<(Self, Self), TensorError>;
    // ... 其他归约操作
}

// src/tensor/advanced.rs - 高级操作
pub trait AdvancedOps<T: Numeric> {
    fn cat(tensors: &[&Self], dim: usize) -> Result<Self, TensorError>;
    fn gather(&self, dim: usize, index: &Self) -> Result<Self, TensorError>;
    fn masked_fill(&self, mask: &Self, value: T) -> Result<Self, TensorError>;
    // ... 其他高级操作
}
```

## 验收标准
1. 所有新增操作都有完整的单元测试
2. 性能基准测试显示合理的执行时间
3. 内存使用优化，无内存泄漏
4. 错误处理完善，提供清晰的错误信息
5. 文档完整，包含使用示例
6. 与现有代码风格保持一致

## 优先级
1. **高优先级**: 基础数学函数、归约操作
2. **中优先级**: 高级张量操作、广播机制
3. **低优先级**: 性能优化、内存池

请按照现有的代码风格和架构模式实现这些功能，确保与项目的整体设计保持一致。
