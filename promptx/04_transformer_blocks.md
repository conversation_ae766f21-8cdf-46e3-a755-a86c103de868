# 提示词 04: 实现Transformer编码器和解码器块

## 任务描述
在ComfyUI推理引擎中实现完整的Transformer编码器和解码器块，组合注意力机制、神经网络层和归一化层，构建标准的Transformer架构。

## 背景信息
前面已经实现了注意力机制和神经网络层。现在需要将这些组件组合成完整的Transformer块，这是构建各种Transformer模型的核心组件。

## 具体要求

### 1. Transformer编码器块
在 `src/transformer/encoder.rs` 中实现：
- **多头自注意力**: 集成多头注意力机制
- **前馈网络**: 位置感知的前馈网络
- **残差连接**: 跨层的残差连接
- **层归一化**: Pre-LN或Post-LN配置
- **Dropout**: 可配置的dropout层

### 2. Transformer解码器块
在 `src/transformer/decoder.rs` 中实现：
- **掩码自注意力**: 因果掩码的自注意力
- **交叉注意力**: 编码器-解码器注意力
- **前馈网络**: 与编码器相同的FFN结构
- **多层归一化**: 每个子层后的归一化
- **残差连接**: 完整的残差连接路径

### 3. Transformer变体支持
在 `src/transformer/variants/` 目录下实现：
- **GPT风格**: 仅解码器的自回归模型
- **BERT风格**: 仅编码器的双向模型
- **T5风格**: 编码器-解码器架构
- **现代变体**: RMSNorm、SwiGLU等优化

### 4. 位置编码集成
在 `src/transformer/position.rs` 中实现：
- **绝对位置编码**: 可学习和固定的位置编码
- **相对位置编码**: T5风格的相对位置偏置
- **旋转位置编码**: RoPE集成到注意力计算中
- **ALiBi**: 注意力线性偏置

## 技术要求

### 架构灵活性
- **可配置层数**: 支持不同深度的模型
- **可配置维度**: 隐藏维度、头数等参数
- **可配置归一化**: Pre-LN、Post-LN、RMSNorm等
- **可配置激活**: 不同的激活函数选择

### 性能优化
- **层融合**: 合并相邻的线性操作
- **内存优化**: 减少中间张量的内存占用
- **并行计算**: 多层和多头的并行处理
- **梯度检查点**: 内存与计算的权衡

### 兼容性设计
- **标准接口**: 与HuggingFace等框架兼容的接口
- **权重加载**: 支持从预训练模型加载权重
- **配置管理**: 统一的模型配置系统

## 代码结构建议

```rust
// src/transformer/encoder.rs
pub struct TransformerEncoderLayer<T: Numeric> {
    self_attention: MultiHeadAttention<T>,
    feed_forward: FeedForward<T>,
    norm1: Box<dyn NormalizationLayer<T>>,
    norm2: Box<dyn NormalizationLayer<T>>,
    dropout: Option<f32>,
    pre_norm: bool,
}

impl<T: Numeric> TransformerEncoderLayer<T> {
    pub fn forward(
        &self,
        input: &Tensor<T>,
        attention_mask: Option<&Tensor<bool>>,
    ) -> Result<Tensor<T>, TransformerError>;
}

pub struct TransformerEncoder<T: Numeric> {
    layers: Vec<TransformerEncoderLayer<T>>,
    final_norm: Option<Box<dyn NormalizationLayer<T>>>,
}

// src/transformer/decoder.rs
pub struct TransformerDecoderLayer<T: Numeric> {
    self_attention: MultiHeadAttention<T>,
    cross_attention: Option<MultiHeadAttention<T>>,
    feed_forward: FeedForward<T>,
    norm1: Box<dyn NormalizationLayer<T>>,
    norm2: Option<Box<dyn NormalizationLayer<T>>>,
    norm3: Box<dyn NormalizationLayer<T>>,
    dropout: Option<f32>,
    pre_norm: bool,
}

// src/transformer/block.rs
pub struct TransformerBlock<T: Numeric> {
    block_type: BlockType,
    encoder_layer: Option<TransformerEncoderLayer<T>>,
    decoder_layer: Option<TransformerDecoderLayer<T>>,
}

#[derive(Debug, Clone)]
pub enum BlockType {
    EncoderOnly,
    DecoderOnly,
    EncoderDecoder,
}
```

## 实现细节

### 1. 编码器层实现
```rust
impl<T: Numeric> TransformerEncoderLayer<T> {
    pub fn forward(
        &self,
        input: &Tensor<T>,
        attention_mask: Option<&Tensor<bool>>,
    ) -> Result<Tensor<T>, TransformerError> {
        let mut x = input.clone();
        
        // Pre-LN: 先归一化
        if self.pre_norm {
            x = self.norm1.forward(&x)?;
        }
        
        // 自注意力
        let attention_output = self.self_attention.forward(&x, &x, &x, attention_mask)?;
        
        // 残差连接
        x = (input + &attention_output)?;
        
        // Post-LN: 后归一化
        if !self.pre_norm {
            x = self.norm1.forward(&x)?;
        }
        
        // 前馈网络部分
        let residual = x.clone();
        
        if self.pre_norm {
            x = self.norm2.forward(&x)?;
        }
        
        let ff_output = self.feed_forward.forward(&x)?;
        x = (&residual + &ff_output)?;
        
        if !self.pre_norm {
            x = self.norm2.forward(&x)?;
        }
        
        Ok(x)
    }
}
```

### 2. 解码器层实现
```rust
impl<T: Numeric> TransformerDecoderLayer<T> {
    pub fn forward(
        &self,
        input: &Tensor<T>,
        encoder_output: Option<&Tensor<T>>,
        self_attention_mask: Option<&Tensor<bool>>,
        cross_attention_mask: Option<&Tensor<bool>>,
    ) -> Result<Tensor<T>, TransformerError> {
        let mut x = input.clone();
        
        // 1. 掩码自注意力
        if self.pre_norm {
            x = self.norm1.forward(&x)?;
        }
        
        let self_attn_output = self.self_attention.forward(&x, &x, &x, self_attention_mask)?;
        x = (input + &self_attn_output)?;
        
        if !self.pre_norm {
            x = self.norm1.forward(&x)?;
        }
        
        // 2. 交叉注意力（如果有编码器输出）
        if let (Some(cross_attention), Some(encoder_out)) = (&self.cross_attention, encoder_output) {
            let residual = x.clone();
            
            if self.pre_norm {
                x = self.norm2.as_ref().unwrap().forward(&x)?;
            }
            
            let cross_attn_output = cross_attention.forward(&x, encoder_out, encoder_out, cross_attention_mask)?;
            x = (&residual + &cross_attn_output)?;
            
            if !self.pre_norm {
                x = self.norm2.as_ref().unwrap().forward(&x)?;
            }
        }
        
        // 3. 前馈网络
        let residual = x.clone();
        
        if self.pre_norm {
            x = self.norm3.forward(&x)?;
        }
        
        let ff_output = self.feed_forward.forward(&x)?;
        x = (&residual + &ff_output)?;
        
        if !self.pre_norm {
            x = self.norm3.forward(&x)?;
        }
        
        Ok(x)
    }
}
```

### 3. 配置系统
```rust
#[derive(Debug, Clone)]
pub struct TransformerConfig {
    pub hidden_size: usize,
    pub num_layers: usize,
    pub num_attention_heads: usize,
    pub intermediate_size: usize,
    pub max_position_embeddings: usize,
    pub vocab_size: usize,
    pub dropout: f32,
    pub attention_dropout: f32,
    pub activation_function: ActivationType,
    pub normalization_type: NormalizationType,
    pub position_encoding_type: PositionEncodingType,
    pub pre_norm: bool,
    pub use_cache: bool,
}

#[derive(Debug, Clone)]
pub enum ActivationType {
    ReLU,
    GELU,
    Swish,
    SwiGLU,
    GeGLU,
}

#[derive(Debug, Clone)]
pub enum NormalizationType {
    LayerNorm,
    RMSNorm,
    BatchNorm,
}
```

## 测试要求

### 单元测试
- 每个Transformer块的前向传播
- 残差连接的正确性
- 不同配置下的行为验证
- 掩码处理的正确性

### 集成测试
- 多层Transformer的堆叠
- 编码器-解码器的交互
- KV缓存的集成测试
- 不同模型变体的测试

### 性能测试
- 不同层数和维度的性能
- 内存使用量测试
- 批处理效率验证

## 验收标准
1. 所有Transformer块通过单元测试
2. 与标准Transformer架构行为一致
3. 支持主流的Transformer变体
4. 性能和内存使用合理
5. 配置系统灵活易用
6. 代码文档完整

## 优先级
1. **最高优先级**: 基础编码器和解码器块
2. **高优先级**: 残差连接、层归一化集成
3. **中优先级**: 配置系统、模型变体支持
4. **低优先级**: 高级优化、特殊位置编码

请确保实现的Transformer块能够作为构建完整模型的基础组件，并与现有的注意力机制和神经网络层完美集成。
