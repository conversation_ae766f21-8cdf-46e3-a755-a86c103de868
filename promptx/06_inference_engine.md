# 提示词 06: 实现推理引擎和批处理系统

## 任务描述
在ComfyUI推理引擎中实现高效的推理引擎，支持批处理推理、KV缓存管理、动态批处理和推理优化等功能。

## 背景信息
当前项目已经实现了Transformer组件和模型加载系统。需要构建高效的推理引擎，将所有组件整合起来，提供高性能的推理服务。

## 具体要求

### 1. 核心推理引擎
在 `src/inference/engine.rs` 中实现：
- **推理会话**: 管理模型实例和推理状态
- **输入预处理**: 文本tokenization和张量转换
- **输出后处理**: 概率分布处理和文本生成
- **推理流水线**: 完整的端到端推理流程

### 2. 批处理系统
在 `src/inference/batching.rs` 中实现：
- **静态批处理**: 固定批大小的批处理推理
- **动态批处理**: 自适应批大小的智能批处理
- **序列打包**: 高效的序列长度处理
- **批处理调度**: 请求排队和批次组装

### 3. KV缓存管理
在 `src/inference/cache.rs` 中实现：
- **缓存分配**: 动态的KV缓存内存管理
- **缓存复用**: 多请求间的缓存共享
- **缓存压缩**: 长序列的缓存压缩策略
- **缓存清理**: 自动的缓存垃圾回收

### 4. 生成策略
在 `src/inference/generation.rs` 中实现：
- **贪心解码**: 简单的贪心生成策略
- **束搜索**: Beam search生成策略
- **采样生成**: Top-k, Top-p, 温度采样
- **约束生成**: 支持生成约束的解码

## 技术要求

### 性能优化
- **并行推理**: 多线程和异步推理
- **内存优化**: 最小化内存分配和拷贝
- **计算优化**: 算子融合和计算图优化
- **IO优化**: 高效的数据传输和序列化

### 可扩展性
- **水平扩展**: 支持多GPU和分布式推理
- **垂直扩展**: 充分利用单机资源
- **弹性伸缩**: 根据负载动态调整资源
- **负载均衡**: 智能的请求分发

### 可观测性
- **性能监控**: 推理延迟、吞吐量等指标
- **资源监控**: CPU、内存、GPU使用率
- **错误监控**: 推理错误和异常统计
- **调试支持**: 详细的推理过程日志

## 代码结构建议

```rust
// src/inference/engine.rs
pub struct InferenceEngine<T: Numeric> {
    model: Box<dyn Model<T>>,
    tokenizer: Box<dyn Tokenizer>,
    device: Device,
    config: InferenceConfig,
    cache_manager: CacheManager<T>,
    batch_scheduler: BatchScheduler,
}

impl<T: Numeric> InferenceEngine<T> {
    pub async fn generate(
        &mut self,
        prompt: &str,
        generation_config: &GenerationConfig,
    ) -> Result<GenerationOutput, InferenceError>;
    
    pub async fn generate_batch(
        &mut self,
        prompts: &[String],
        generation_config: &GenerationConfig,
    ) -> Result<Vec<GenerationOutput>, InferenceError>;
    
    pub async fn generate_stream(
        &mut self,
        prompt: &str,
        generation_config: &GenerationConfig,
    ) -> Result<impl Stream<Item = Result<Token, InferenceError>>, InferenceError>;
}

// src/inference/batching.rs
pub struct BatchScheduler {
    max_batch_size: usize,
    max_sequence_length: usize,
    timeout_ms: u64,
    pending_requests: VecDeque<InferenceRequest>,
}

impl BatchScheduler {
    pub fn add_request(&mut self, request: InferenceRequest) -> RequestId;
    pub fn get_next_batch(&mut self) -> Option<InferenceBatch>;
    pub fn complete_request(&mut self, request_id: RequestId, output: GenerationOutput);
}

// src/inference/cache.rs
pub struct CacheManager<T: Numeric> {
    cache_pools: HashMap<String, KVCachePool<T>>,
    max_cache_size: usize,
    eviction_policy: EvictionPolicy,
}

pub struct KVCachePool<T: Numeric> {
    caches: Vec<KVCache<T>>,
    free_list: Vec<usize>,
    allocation_map: HashMap<RequestId, usize>,
}
```

## 实现细节

### 1. 推理引擎核心
```rust
impl<T: Numeric> InferenceEngine<T> {
    pub async fn generate(
        &mut self,
        prompt: &str,
        generation_config: &GenerationConfig,
    ) -> Result<GenerationOutput, InferenceError> {
        // 1. 预处理
        let input_ids = self.tokenizer.encode(prompt)?;
        let input_tensor = Tensor::from_vec(input_ids, &Shape::new(vec![1, input_ids.len()]))?;
        
        // 2. 分配KV缓存
        let cache = self.cache_manager.allocate_cache(input_tensor.shape())?;
        
        // 3. 生成循环
        let mut generated_tokens = Vec::new();
        let mut current_input = input_tensor;
        
        for step in 0..generation_config.max_new_tokens {
            // 前向传播
            let logits = self.model.forward(&current_input, Some(&cache))?;
            
            // 应用生成策略
            let next_token = self.apply_generation_strategy(&logits, generation_config)?;
            generated_tokens.push(next_token);
            
            // 检查停止条件
            if self.should_stop(&next_token, generation_config) {
                break;
            }
            
            // 准备下一步输入
            current_input = Tensor::from_vec(vec![next_token], &Shape::new(vec![1, 1]))?;
        }
        
        // 4. 后处理
        let output_text = self.tokenizer.decode(&generated_tokens)?;
        
        // 5. 释放缓存
        self.cache_manager.release_cache(cache);
        
        Ok(GenerationOutput {
            text: output_text,
            tokens: generated_tokens,
            finish_reason: FinishReason::MaxTokens,
        })
    }
}
```

### 2. 动态批处理
```rust
impl BatchScheduler {
    pub fn get_next_batch(&mut self) -> Option<InferenceBatch> {
        if self.pending_requests.is_empty() {
            return None;
        }
        
        let mut batch = InferenceBatch::new();
        let mut total_tokens = 0;
        
        while let Some(request) = self.pending_requests.front() {
            let request_tokens = request.input_length + request.max_new_tokens;
            
            // 检查批次大小和token限制
            if batch.size() >= self.max_batch_size || 
               total_tokens + request_tokens > self.max_sequence_length {
                break;
            }
            
            let request = self.pending_requests.pop_front().unwrap();
            total_tokens += request_tokens;
            batch.add_request(request);
        }
        
        if batch.is_empty() {
            None
        } else {
            Some(batch)
        }
    }
}
```

### 3. 生成策略
```rust
impl<T: Numeric> InferenceEngine<T> {
    fn apply_generation_strategy(
        &self,
        logits: &Tensor<T>,
        config: &GenerationConfig,
    ) -> Result<u32, InferenceError> {
        match &config.strategy {
            GenerationStrategy::Greedy => {
                let token_id = logits.argmax(-1)?;
                Ok(token_id.to_scalar()? as u32)
            },
            GenerationStrategy::Sample { temperature, top_k, top_p } => {
                let mut probs = logits.softmax(-1)?;
                
                // 应用温度
                if *temperature != 1.0 {
                    probs = (logits / *temperature)?.softmax(-1)?;
                }
                
                // Top-k过滤
                if let Some(k) = top_k {
                    probs = self.apply_top_k_filter(&probs, *k)?;
                }
                
                // Top-p过滤
                if let Some(p) = top_p {
                    probs = self.apply_top_p_filter(&probs, *p)?;
                }
                
                // 采样
                self.sample_from_distribution(&probs)
            },
            GenerationStrategy::BeamSearch { num_beams } => {
                // 实现束搜索
                self.beam_search(logits, *num_beams)
            },
        }
    }
}
```

### 4. 流式生成
```rust
impl<T: Numeric> InferenceEngine<T> {
    pub async fn generate_stream(
        &mut self,
        prompt: &str,
        generation_config: &GenerationConfig,
    ) -> Result<impl Stream<Item = Result<Token, InferenceError>>, InferenceError> {
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        
        let engine = self.clone();
        let prompt = prompt.to_string();
        let config = generation_config.clone();
        
        tokio::spawn(async move {
            let mut engine = engine;
            
            // 预处理
            let input_ids = engine.tokenizer.encode(&prompt).unwrap();
            let mut current_input = Tensor::from_vec(input_ids, &Shape::new(vec![1, input_ids.len()])).unwrap();
            let cache = engine.cache_manager.allocate_cache(current_input.shape()).unwrap();
            
            // 生成循环
            for step in 0..config.max_new_tokens {
                let logits = engine.model.forward(&current_input, Some(&cache)).unwrap();
                let next_token = engine.apply_generation_strategy(&logits, &config).unwrap();
                
                // 发送token
                if tx.send(Ok(Token { id: next_token, text: engine.tokenizer.decode_token(next_token).unwrap() })).await.is_err() {
                    break;
                }
                
                // 检查停止条件
                if engine.should_stop(&next_token, &config) {
                    break;
                }
                
                current_input = Tensor::from_vec(vec![next_token], &Shape::new(vec![1, 1])).unwrap();
            }
            
            engine.cache_manager.release_cache(cache);
        });
        
        Ok(tokio_stream::wrappers::ReceiverStream::new(rx))
    }
}
```

## 配置系统

```rust
#[derive(Debug, Clone)]
pub struct InferenceConfig {
    pub max_batch_size: usize,
    pub max_sequence_length: usize,
    pub cache_size_mb: usize,
    pub timeout_ms: u64,
    pub enable_streaming: bool,
    pub enable_batching: bool,
}

#[derive(Debug, Clone)]
pub struct GenerationConfig {
    pub max_new_tokens: usize,
    pub min_new_tokens: usize,
    pub strategy: GenerationStrategy,
    pub stop_tokens: Vec<u32>,
    pub repetition_penalty: f32,
    pub length_penalty: f32,
}

#[derive(Debug, Clone)]
pub enum GenerationStrategy {
    Greedy,
    Sample {
        temperature: f32,
        top_k: Option<usize>,
        top_p: Option<f32>,
    },
    BeamSearch {
        num_beams: usize,
    },
}
```

## 测试要求

### 单元测试
- 各个推理组件的功能测试
- 生成策略的正确性验证
- 缓存管理的测试
- 批处理逻辑的测试

### 集成测试
- 端到端推理流程测试
- 多种生成策略的集成测试
- 流式生成的测试
- 并发推理的安全性测试

### 性能测试
- 推理延迟和吞吐量基准
- 内存使用量测试
- 批处理效率验证
- 缓存命中率测试

## 验收标准
1. 推理引擎功能完整，支持各种生成策略
2. 批处理系统高效，能够提升整体吞吐量
3. KV缓存管理正确，内存使用合理
4. 流式生成工作正常，延迟低
5. 性能满足预期，可扩展性良好
6. 代码文档完整，易于使用和维护

## 优先级
1. **最高优先级**: 核心推理引擎、基础生成策略
2. **高优先级**: KV缓存管理、批处理系统
3. **中优先级**: 流式生成、高级生成策略
4. **低优先级**: 分布式推理、高级优化

请确保实现的推理引擎能够高效地整合所有组件，为用户提供高性能、易用的推理服务。
